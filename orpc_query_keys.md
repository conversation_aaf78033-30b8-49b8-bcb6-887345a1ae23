# oRPC Tanstack Query Query/Mutation Keys

oRPC provides a set of helper methods to generate keys for queries and mutations:

- .key: Generate a partial matching key for actions like revalidating queries, checking mutation status, etc.
- .queryKey: Generate a full matching key for Query Options.
- .streamedKey: Generate a full matching key for Streamed Query Options.
- .infiniteKey: Generate a full matching key for Infinite Query Options.
- .mutationKey: Generate a full matching key for Mutation Options.

Example:

```typescript
const queryClient = useQueryClient();

// Invalidate all planet queries
queryClient.invalidateQueries({
    queryKey: orpc.planet.key(),
});

// Invalidate only regular (non-infinite) planet queries
queryClient.invalidateQueries({
    queryKey: orpc.planet.key({ type: "query" }),
});

// Invalidate the planet find query with id 123
queryClient.invalidateQueries({
    queryKey: orpc.planet.find.key({ input: { id: 123 } }),
});

// Update the planet find query with id 123
queryClient.setQueryData(orpc.planet.find.queryKey({ input: { id: 123 } }), (old) => {
    return { ...old, id: 123, name: "Earth" };
});
```
